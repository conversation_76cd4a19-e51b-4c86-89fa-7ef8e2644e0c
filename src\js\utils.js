/**
 * Utility Functions
 * Common helper functions used throughout the application
 */

const Utils = {
  
  /**
   * DOM Manipulation Utilities
   */
  DOM: {
    /**
     * Get element by ID
     * @param {string} id - Element ID
     * @returns {HTMLElement|null}
     */
    getElementById(id) {
      return document.getElementById(id);
    },
    
    /**
     * Get element by selector
     * @param {string} selector - CSS selector
     * @returns {HTMLElement|null}
     */
    querySelector(selector) {
      return document.querySelector(selector);
    },
    
    /**
     * Get elements by selector
     * @param {string} selector - CSS selector
     * @returns {NodeList}
     */
    querySelectorAll(selector) {
      return document.querySelectorAll(selector);
    },
    
    /**
     * Add class to element
     * @param {HTMLElement} element - Target element
     * @param {string} className - Class name to add
     */
    addClass(element, className) {
      if (element && className) {
        element.classList.add(className);
      }
    },
    
    /**
     * Remove class from element
     * @param {HTMLElement} element - Target element
     * @param {string} className - Class name to remove
     */
    removeClass(element, className) {
      if (element && className) {
        element.classList.remove(className);
      }
    },
    
    /**
     * Toggle class on element
     * @param {HTMLElement} element - Target element
     * @param {string} className - Class name to toggle
     */
    toggleClass(element, className) {
      if (element && className) {
        element.classList.toggle(className);
      }
    },
    
    /**
     * Check if element has class
     * @param {HTMLElement} element - Target element
     * @param {string} className - Class name to check
     * @returns {boolean}
     */
    hasClass(element, className) {
      return element && className ? element.classList.contains(className) : false;
    }
  },
  
  /**
   * Animation Utilities
   */
  Animation: {
    /**
     * Add animation class to element
     * @param {HTMLElement} element - Target element
     * @param {string} animationClass - Animation class name
     * @param {number} delay - Delay in milliseconds
     */
    animate(element, animationClass, delay = 0) {
      if (!element || !animationClass) return;
      
      setTimeout(() => {
        Utils.DOM.addClass(element, animationClass);
      }, delay);
    },
    
    /**
     * Remove animation class after animation completes
     * @param {HTMLElement} element - Target element
     * @param {string} animationClass - Animation class name
     * @param {number} duration - Animation duration in milliseconds
     */
    animateOnce(element, animationClass, duration = 600) {
      if (!element || !animationClass) return;
      
      Utils.DOM.addClass(element, animationClass);
      
      setTimeout(() => {
        Utils.DOM.removeClass(element, animationClass);
      }, duration);
    },
    
    /**
     * Animate counter from 0 to target value
     * @param {HTMLElement} element - Target element
     * @param {number} target - Target number
     * @param {number} duration - Animation duration in milliseconds
     */
    animateCounter(element, target, duration = 2000) {
      if (!element || typeof target !== 'number') return;
      
      const start = 0;
      const increment = target / (duration / 16); // 60fps
      let current = start;
      
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        element.textContent = Math.floor(current).toLocaleString();
      }, 16);
    }
  },
  
  /**
   * Validation Utilities
   */
  Validation: {
    /**
     * Validate username
     * @param {string} username - Username to validate
     * @returns {object} Validation result
     */
    validateUsername(username) {
      const rules = CONFIG.VALIDATION.USERNAME;
      
      if (!username || username.trim() === '') {
        return { valid: false, message: CONFIG.ERRORS.USERNAME_REQUIRED };
      }
      
      if (username.length < rules.MIN_LENGTH) {
        return { valid: false, message: CONFIG.ERRORS.USERNAME_TOO_SHORT };
      }
      
      if (username.length > rules.MAX_LENGTH) {
        return { valid: false, message: CONFIG.ERRORS.USERNAME_TOO_LONG };
      }
      
      if (!rules.PATTERN.test(username)) {
        return { valid: false, message: CONFIG.ERRORS.USERNAME_INVALID };
      }
      
      if (rules.FORBIDDEN_WORDS.includes(username.toLowerCase())) {
        return { valid: false, message: CONFIG.ERRORS.USERNAME_FORBIDDEN };
      }
      
      return { valid: true, message: CONFIG.SUCCESS.USERNAME_VALID };
    }
  },
  
  /**
   * Storage Utilities
   */
  Storage: {
    /**
     * Save data to localStorage
     * @param {string} key - Storage key
     * @param {any} data - Data to save
     */
    save(key, data) {
      try {
        localStorage.setItem(key, JSON.stringify(data));
      } catch (error) {
        console.error('Failed to save to localStorage:', error);
      }
    },
    
    /**
     * Load data from localStorage
     * @param {string} key - Storage key
     * @returns {any} Stored data or null
     */
    load(key) {
      try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
      } catch (error) {
        console.error('Failed to load from localStorage:', error);
        return null;
      }
    },
    
    /**
     * Remove data from localStorage
     * @param {string} key - Storage key
     */
    remove(key) {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.error('Failed to remove from localStorage:', error);
      }
    }
  },
  
  /**
   * Number Utilities
   */
  Numbers: {
    /**
     * Generate random number between min and max
     * @param {number} min - Minimum value
     * @param {number} max - Maximum value
     * @returns {number} Random number
     */
    random(min, max) {
      return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    /**
     * Format number with commas
     * @param {number} num - Number to format
     * @returns {string} Formatted number
     */
    formatNumber(num) {
      return num.toLocaleString();
    },
    
    /**
     * Clamp number between min and max
     * @param {number} num - Number to clamp
     * @param {number} min - Minimum value
     * @param {number} max - Maximum value
     * @returns {number} Clamped number
     */
    clamp(num, min, max) {
      return Math.min(Math.max(num, min), max);
    }
  },
  
  /**
   * Time Utilities
   */
  Time: {
    /**
     * Delay execution
     * @param {number} ms - Milliseconds to delay
     * @returns {Promise} Promise that resolves after delay
     */
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },
    
    /**
     * Get current timestamp
     * @returns {number} Current timestamp
     */
    now() {
      return Date.now();
    }
  },
  
  /**
   * Debug Utilities
   */
  Debug: {
    /**
     * Log message if debug is enabled
     * @param {string} level - Log level
     * @param {string} message - Message to log
     * @param {any} data - Additional data
     */
    log(level, message, data = null) {
      if (!CONFIG.DEBUG.ENABLED) return;
      
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
      
      switch (level) {
        case 'debug':
          console.debug(logMessage, data);
          break;
        case 'info':
          console.info(logMessage, data);
          break;
        case 'warn':
          console.warn(logMessage, data);
          break;
        case 'error':
          console.error(logMessage, data);
          break;
        default:
          console.log(logMessage, data);
      }
    }
  }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Utils;
} else if (typeof window !== 'undefined') {
  window.Utils = Utils;
}
