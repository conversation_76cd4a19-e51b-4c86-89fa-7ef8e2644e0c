/**
 * Generator Controller
 * Handles the gem generation process and UI updates
 */

const GeneratorController = {
  
  currentStep: 1,
  maxSteps: 3,
  generationInProgress: false,
  
  /**
   * Initialize generator
   */
  init() {
    this.setupStepNavigation();
    this.setupGenerationProcess();
    this.updateStatistics();
    Utils.Debug.log('info', 'Generator controller initialized');
  },
  
  /**
   * Setup step navigation
   */
  setupStepNavigation() {
    const nextBtn = Utils.DOM.getElementById('next-btn');
    const prevBtn = Utils.DOM.getElementById('prev-btn');
    const generateBtn = Utils.DOM.getElementById('generate-btn');
    
    if (nextBtn) {
      nextBtn.addEventListener('click', () => {
        this.nextStep();
      });
    }
    
    if (prevBtn) {
      prevBtn.addEventListener('click', () => {
        this.prevStep();
      });
    }
    
    if (generateBtn) {
      generateBtn.addEventListener('click', () => {
        this.startGeneration();
      });
    }
    
    // Initialize step display
    this.updateStepDisplay();
  },
  
  /**
   * Setup generation process
   */
  setupGenerationProcess() {
    const restartBtn = Utils.DOM.getElementById('restart-btn');
    const closeBtn = Utils.DOM.getElementById('close-btn');
    
    if (restartBtn) {
      restartBtn.addEventListener('click', () => {
        this.restartProcess();
      });
    }
    
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.closeProcess();
      });
    }
  },
  
  /**
   * Move to next step
   */
  nextStep() {
    if (this.generationInProgress) return;
    
    // Validate current step
    const validation = ValidationController.validateStep(this.currentStep);
    
    if (!validation.valid) {
      Utils.Debug.log('warn', 'Step validation failed', validation);
      return;
    }
    
    if (this.currentStep < this.maxSteps) {
      AnimationController.animateStepTransition(this.currentStep, this.currentStep + 1);
      this.currentStep++;
      this.updateStepDisplay();
      
      Utils.Debug.log('info', 'Moved to next step', { step: this.currentStep });
    }
  },
  
  /**
   * Move to previous step
   */
  prevStep() {
    if (this.generationInProgress) return;
    
    if (this.currentStep > 1) {
      AnimationController.animateStepTransition(this.currentStep, this.currentStep - 1);
      this.currentStep--;
      this.updateStepDisplay();
      
      Utils.Debug.log('info', 'Moved to previous step', { step: this.currentStep });
    }
  },
  
  /**
   * Update step display and buttons
   */
  updateStepDisplay() {
    const nextBtn = Utils.DOM.getElementById('next-btn');
    const prevBtn = Utils.DOM.getElementById('prev-btn');
    const generateBtn = Utils.DOM.getElementById('generate-btn');
    
    // Update button visibility
    if (prevBtn) {
      if (this.currentStep > 1) {
        Utils.DOM.removeClass(prevBtn, 'hidden');
      } else {
        Utils.DOM.addClass(prevBtn, 'hidden');
      }
    }
    
    if (nextBtn && generateBtn) {
      if (this.currentStep < this.maxSteps) {
        Utils.DOM.removeClass(nextBtn, 'hidden');
        Utils.DOM.addClass(generateBtn, 'hidden');
      } else {
        Utils.DOM.addClass(nextBtn, 'hidden');
        Utils.DOM.removeClass(generateBtn, 'hidden');
      }
    }
  },
  
  /**
   * Start generation process
   */
  async startGeneration() {
    if (this.generationInProgress) return;
    
    // Final validation
    const validation = ValidationController.validateAll();
    
    if (!validation.valid) {
      Utils.Debug.log('error', 'Final validation failed', validation);
      return;
    }
    
    this.generationInProgress = true;
    const formData = validation.data;
    
    Utils.Debug.log('info', 'Starting generation process', formData);
    
    // Show generation modal
    AnimationController.showModal('generation-modal');
    
    try {
      // Simulate generation process
      await this.simulateGeneration(formData);
      
      // Show success
      this.showSuccess(formData);
      
    } catch (error) {
      Utils.Debug.log('error', 'Generation failed', error);
      this.showError(error.message || CONFIG.ERRORS.GENERATION_FAILED);
    } finally {
      this.generationInProgress = false;
    }
  },
  
  /**
   * Simulate generation process
   * @param {object} formData - Form data
   */
  async simulateGeneration(formData) {
    const progressCircle = Utils.DOM.querySelector('.progress-ring-circle');
    const progressPercentage = Utils.DOM.querySelector('.progress-percentage');
    const statusText = Utils.DOM.querySelector('.status-text');
    const statusSteps = Utils.DOM.querySelectorAll('.status-step');
    
    const messages = CONFIG.UI.LOADING_MESSAGES;
    const totalTime = CONFIG.GENERATION.GENERATION_TIME;
    const updateInterval = CONFIG.GENERATION.PROGRESS_INTERVAL;
    const totalUpdates = totalTime / updateInterval;
    
    let currentUpdate = 0;
    let currentMessageIndex = 0;
    
    return new Promise((resolve) => {
      const updateProgress = () => {
        currentUpdate++;
        const progress = (currentUpdate / totalUpdates) * 100;
        
        // Update progress circle
        if (progressCircle && progressPercentage) {
          AnimationController.animateProgressCircle(progressCircle, progress);
          progressPercentage.textContent = `${Math.floor(progress)}%`;
        }
        
        // Update status message
        const messageIndex = Math.floor((progress / 100) * messages.length);
        if (messageIndex !== currentMessageIndex && messageIndex < messages.length) {
          currentMessageIndex = messageIndex;
          
          if (statusText) {
            statusText.textContent = messages[messageIndex];
          }
          
          // Update status steps
          if (statusSteps[messageIndex]) {
            statusSteps.forEach((step, index) => {
              Utils.DOM.removeClass(step, 'active');
              Utils.DOM.removeClass(step, 'completed');
              
              if (index < messageIndex) {
                Utils.DOM.addClass(step, 'completed');
              } else if (index === messageIndex) {
                Utils.DOM.addClass(step, 'active');
              }
            });
          }
        }
        
        if (currentUpdate >= totalUpdates) {
          // Mark all steps as completed
          statusSteps.forEach(step => {
            Utils.DOM.removeClass(step, 'active');
            Utils.DOM.addClass(step, 'completed');
          });
          
          resolve();
        } else {
          setTimeout(updateProgress, updateInterval);
        }
      };
      
      updateProgress();
    });
  },
  
  /**
   * Show success modal
   * @param {object} formData - Form data
   */
  showSuccess(formData) {
    // Hide generation modal
    AnimationController.hideModal('generation-modal');
    
    // Update success modal with data
    const finalUsername = Utils.DOM.getElementById('final-username');
    const finalPlatform = Utils.DOM.getElementById('final-platform');
    const finalGems = Utils.DOM.getElementById('final-gems');
    
    if (finalUsername) finalUsername.textContent = formData.username;
    if (finalPlatform) finalPlatform.textContent = formData.platformName;
    if (finalGems) finalGems.textContent = Utils.Numbers.formatNumber(formData.gemAmount);
    
    // Show success modal
    setTimeout(() => {
      AnimationController.showModal('success-modal');
    }, 500);
    
    // Save to storage
    Utils.Storage.save(CONFIG.STORAGE_KEYS.USER_DATA, formData);
    
    Utils.Debug.log('info', 'Generation completed successfully', formData);
  },
  
  /**
   * Show error message
   * @param {string} message - Error message
   */
  showError(message) {
    // Hide generation modal
    AnimationController.hideModal('generation-modal');
    
    // Show error (you can implement a custom error modal)
    alert(message);
    
    Utils.Debug.log('error', 'Generation error shown', { message });
  },
  
  /**
   * Restart the process
   */
  restartProcess() {
    // Hide success modal
    AnimationController.hideModal('success-modal');
    
    // Reset form and steps
    ValidationController.resetForm();
    this.currentStep = 1;
    this.updateStepDisplay();
    
    // Reset step containers
    const stepContainers = Utils.DOM.querySelectorAll('.step-container');
    stepContainers.forEach((container, index) => {
      Utils.DOM.removeClass(container, 'active');
      if (index === 0) {
        Utils.DOM.addClass(container, 'active');
      }
    });
    
    Utils.Debug.log('info', 'Process restarted');
  },
  
  /**
   * Close the process
   */
  closeProcess() {
    // Hide success modal
    AnimationController.hideModal('success-modal');
    
    Utils.Debug.log('info', 'Process closed');
  },
  
  /**
   * Update statistics display
   */
  updateStatistics() {
    const updateStats = () => {
      const usersOnline = Utils.DOM.querySelector('[data-count]');
      const gemsGenerated = Utils.DOM.querySelectorAll('[data-count]')[1];
      
      if (usersOnline) {
        const currentUsers = parseInt(usersOnline.textContent.replace(/,/g, '')) || 0;
        const newUsers = currentUsers + Utils.Numbers.random(1, 10);
        usersOnline.textContent = Utils.Numbers.formatNumber(newUsers);
      }
      
      if (gemsGenerated) {
        const currentGems = parseInt(gemsGenerated.textContent.replace(/,/g, '')) || 0;
        const newGems = currentGems + Utils.Numbers.random(100, 1000);
        gemsGenerated.textContent = Utils.Numbers.formatNumber(newGems);
      }
    };
    
    // Update stats periodically
    setInterval(updateStats, CONFIG.STATS.USERS_ONLINE.updateInterval);
  }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GeneratorController;
} else if (typeof window !== 'undefined') {
  window.GeneratorController = GeneratorController;
}
