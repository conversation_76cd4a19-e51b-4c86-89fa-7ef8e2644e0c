/**
 * Configuration file for Brawl Stars Generator
 * Contains all configurable settings and constants
 */

// Application Configuration
const APP_CONFIG = {
    // Sound settings
    SOUND_ENABLED: true,
    SOUND_PATH: 'assets/a/',
    
    // Animation settings
    ANIMATION_DELAYS: {
        STEP_1: 100,
        STEP_2: 200,
        STEP_3: 300,
        STEP_4: 400,
        STEP_5: 500
    },
    
    // Loading screen settings
    LOADING_DELAYS: {
        TRIANGLE_MOVE: 700,
        PROGRESS_BAR: 2000,
        FADE_OUT: 2300
    },
    
    // Generation settings
    GENERATION: {
        MIN_GEMS: 1000,
        MAX_GEMS: 50000,
        GENERATION_SPEED: 2500,
        REFRESH_INTERVAL: 10
    },
    
    // Platform IDs
    PLATFORMS: {
        ANDROID: 4,
        IOS: 5
    },
    
    // Validation settings
    VALIDATION: {
        MIN_USERNAME_LENGTH: 3,
        MAX_USERNAME_LENGTH: 20,
        ALLOWED_CHARACTERS: /^[a-zA-Z0-9_-]+$/
    }
};

// Sound configuration
const SOUND_CONFIG = {
    sounds: [
        { name: "b1", path: APP_CONFIG.SOUND_PATH, volume: 1 },
        { name: "b2", path: APP_CONFIG.SOUND_PATH, volume: 1 },
        { name: "a1", path: APP_CONFIG.SOUND_PATH, volume: 1 },
        { name: "a2", path: APP_CONFIG.SOUND_PATH, volume: 1 },
        { name: "c2", path: APP_CONFIG.SOUND_PATH, volume: 1 },
        { name: "s1", path: APP_CONFIG.SOUND_PATH, volume: 1 },
        { name: "s2", path: APP_CONFIG.SOUND_PATH, volume: 1 }
    ]
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APP_CONFIG, SOUND_CONFIG };
}
