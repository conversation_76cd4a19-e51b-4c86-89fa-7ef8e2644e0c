/**
 * Validation Controller
 * Handles form validation and user input validation
 */

const ValidationController = {
  
  /**
   * Initialize validation
   */
  init() {
    this.setupUsernameValidation();
    this.setupPlatformValidation();
    this.setupGemValidation();
    Utils.Debug.log('info', 'Validation controller initialized');
  },
  
  /**
   * Setup username validation
   */
  setupUsernameValidation() {
    const usernameInput = Utils.DOM.getElementById('username');
    const validationMessage = Utils.DOM.querySelector('.input-validation .validation-message');
    
    if (!usernameInput || !validationMessage) return;
    
    // Real-time validation
    usernameInput.addEventListener('input', (e) => {
      const username = e.target.value.trim();
      const result = Utils.Validation.validateUsername(username);
      
      this.showValidationResult(usernameInput, validationMessage, result);
    });
    
    // Validation on blur
    usernameInput.addEventListener('blur', (e) => {
      const username = e.target.value.trim();
      const result = Utils.Validation.validateUsername(username);
      
      this.showValidationResult(usernameInput, validationMessage, result);
    });
  },
  
  /**
   * Setup platform validation
   */
  setupPlatformValidation() {
    const platformOptions = Utils.DOM.querySelectorAll('.platform-option');
    const validationMessage = Utils.DOM.querySelector('.platform-validation .validation-message');
    
    if (!platformOptions.length || !validationMessage) return;
    
    platformOptions.forEach(option => {
      option.addEventListener('click', () => {
        // Remove selection from all options
        platformOptions.forEach(opt => {
          Utils.DOM.removeClass(opt, 'selected');
        });
        
        // Add selection to clicked option
        Utils.DOM.addClass(option, 'selected');
        
        // Animate selection
        AnimationController.animateSelection(option);
        
        // Clear validation message
        this.clearValidationMessage(validationMessage);
        
        Utils.Debug.log('info', 'Platform selected', {
          platform: option.getAttribute('data-platform')
        });
      });
    });
  },
  
  /**
   * Setup gem amount validation
   */
  setupGemValidation() {
    const gemOptions = Utils.DOM.querySelectorAll('.gem-option');
    
    if (!gemOptions.length) return;
    
    gemOptions.forEach(option => {
      option.addEventListener('click', () => {
        // Remove selection from all options
        gemOptions.forEach(opt => {
          Utils.DOM.removeClass(opt, 'selected');
        });
        
        // Add selection to clicked option
        Utils.DOM.addClass(option, 'selected');
        
        // Animate selection
        AnimationController.animateSelection(option);
        
        // Create sparkle effect
        AnimationController.createSparkleEffect(option);
        
        Utils.Debug.log('info', 'Gem amount selected', {
          amount: option.getAttribute('data-amount')
        });
      });
    });
  },
  
  /**
   * Show validation result
   * @param {HTMLElement} input - Input element
   * @param {HTMLElement} messageElement - Message element
   * @param {object} result - Validation result
   */
  showValidationResult(input, messageElement, result) {
    if (!input || !messageElement) return;
    
    // Clear previous classes
    Utils.DOM.removeClass(input, 'error');
    Utils.DOM.removeClass(input, 'success');
    Utils.DOM.removeClass(messageElement, 'success');
    
    if (result.valid) {
      Utils.DOM.addClass(input, 'success');
      Utils.DOM.addClass(messageElement, 'success');
      messageElement.textContent = result.message;
      
      // Animate success
      AnimationController.animateSuccess(input);
    } else {
      Utils.DOM.addClass(input, 'error');
      messageElement.textContent = result.message;
      
      // Animate error
      AnimationController.animateError(input);
    }
  },
  
  /**
   * Clear validation message
   * @param {HTMLElement} messageElement - Message element
   */
  clearValidationMessage(messageElement) {
    if (messageElement) {
      messageElement.textContent = '';
      Utils.DOM.removeClass(messageElement, 'success');
    }
  },
  
  /**
   * Validate current step
   * @param {number} step - Step number to validate
   * @returns {object} Validation result
   */
  validateStep(step) {
    switch (step) {
      case 1:
        return this.validateUsernameStep();
      case 2:
        return this.validatePlatformStep();
      case 3:
        return this.validateGemStep();
      default:
        return { valid: false, message: 'Invalid step' };
    }
  },
  
  /**
   * Validate username step
   * @returns {object} Validation result
   */
  validateUsernameStep() {
    const usernameInput = Utils.DOM.getElementById('username');
    const validationMessage = Utils.DOM.querySelector('.input-validation .validation-message');
    
    if (!usernameInput) {
      return { valid: false, message: CONFIG.ERRORS.USERNAME_REQUIRED };
    }
    
    const username = usernameInput.value.trim();
    const result = Utils.Validation.validateUsername(username);
    
    this.showValidationResult(usernameInput, validationMessage, result);
    
    return result;
  },
  
  /**
   * Validate platform step
   * @returns {object} Validation result
   */
  validatePlatformStep() {
    const selectedPlatform = Utils.DOM.querySelector('.platform-option.selected');
    const validationMessage = Utils.DOM.querySelector('.platform-validation .validation-message');
    
    if (!selectedPlatform) {
      if (validationMessage) {
        validationMessage.textContent = CONFIG.ERRORS.PLATFORM_REQUIRED;
      }
      return { valid: false, message: CONFIG.ERRORS.PLATFORM_REQUIRED };
    }
    
    this.clearValidationMessage(validationMessage);
    return { valid: true, message: CONFIG.SUCCESS.PLATFORM_SELECTED };
  },
  
  /**
   * Validate gem step
   * @returns {object} Validation result
   */
  validateGemStep() {
    const selectedGem = Utils.DOM.querySelector('.gem-option.selected');
    
    if (!selectedGem) {
      return { valid: false, message: CONFIG.ERRORS.GEMS_REQUIRED };
    }
    
    return { valid: true, message: CONFIG.SUCCESS.GEMS_SELECTED };
  },
  
  /**
   * Get form data
   * @returns {object} Form data
   */
  getFormData() {
    const usernameInput = Utils.DOM.getElementById('username');
    const selectedPlatform = Utils.DOM.querySelector('.platform-option.selected');
    const selectedGem = Utils.DOM.querySelector('.gem-option.selected');
    
    return {
      username: usernameInput ? usernameInput.value.trim() : '',
      platform: selectedPlatform ? selectedPlatform.getAttribute('data-platform') : '',
      platformName: selectedPlatform ? selectedPlatform.querySelector('.platform-name').textContent : '',
      gemAmount: selectedGem ? parseInt(selectedGem.getAttribute('data-amount')) : 0,
      timestamp: Utils.Time.now()
    };
  },
  
  /**
   * Reset form
   */
  resetForm() {
    // Reset username
    const usernameInput = Utils.DOM.getElementById('username');
    if (usernameInput) {
      usernameInput.value = '';
      Utils.DOM.removeClass(usernameInput, 'error');
      Utils.DOM.removeClass(usernameInput, 'success');
    }
    
    // Reset platform selection
    const platformOptions = Utils.DOM.querySelectorAll('.platform-option');
    platformOptions.forEach(option => {
      Utils.DOM.removeClass(option, 'selected');
    });
    
    // Reset gem selection
    const gemOptions = Utils.DOM.querySelectorAll('.gem-option');
    gemOptions.forEach(option => {
      Utils.DOM.removeClass(option, 'selected');
    });
    
    // Clear validation messages
    const validationMessages = Utils.DOM.querySelectorAll('.validation-message');
    validationMessages.forEach(message => {
      message.textContent = '';
      Utils.DOM.removeClass(message, 'success');
    });
    
    Utils.Debug.log('info', 'Form reset');
  },
  
  /**
   * Validate all steps
   * @returns {object} Overall validation result
   */
  validateAll() {
    const step1 = this.validateStep(1);
    const step2 = this.validateStep(2);
    const step3 = this.validateStep(3);
    
    const allValid = step1.valid && step2.valid && step3.valid;
    
    return {
      valid: allValid,
      steps: {
        username: step1,
        platform: step2,
        gems: step3
      },
      data: allValid ? this.getFormData() : null
    };
  }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ValidationController;
} else if (typeof window !== 'undefined') {
  window.ValidationController = ValidationController;
}
