<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Modern Brawl Stars Gems Generator - Get unlimited gems and resources for free!">
    <meta name="keywords" content="brawl stars, gems, generator, free, resources, game">
    <meta name="author" content="Brawl Stars Generator">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Brawl Stars Gems Generator">
    <meta property="og:description" content="Generate unlimited gems and resources for Brawl Stars - Fast, Safe, and Free!">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://yoursite.com">
    <meta property="og:image" content="src/images/og-image.jpg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Brawl Stars Gems Generator">
    <meta name="twitter:description" content="Generate unlimited gems and resources for Brawl Stars - Fast, Safe, and Free!">
    <meta name="twitter:image" content="src/images/twitter-card.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="src/images/favicon.svg">
    <link rel="icon" type="image/x-icon" href="src/images/favicon.svg">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="src/css/variables.css">
    <link rel="stylesheet" href="src/css/reset.css">
    <link rel="stylesheet" href="src/css/components.css">
    <link rel="stylesheet" href="src/css/animations.css">
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/responsive.css">
    
    <title>Brawl Stars Gems Generator - Free & Unlimited</title>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <img src="src/images/placeholder.svg" alt="Brawl Stars" class="logo-img">
            </div>
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
            <div class="loading-text">
                <h2>Loading Generator...</h2>
                <p>Preparing your gems</p>
            </div>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <span class="progress-text">0%</span>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <main id="app" class="app hidden">
        <!-- Header -->
        <header class="header">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <img src="src/images/placeholder.svg" alt="Brawl Stars" class="logo-img">
                        <div class="logo-text">
                            <h1>Brawl Stars</h1>
                            <span>Gems Generator</span>
                        </div>
                    </div>
                    <div class="header-stats">
                        <div class="stat-item">
                            <span class="stat-number" data-count="125847">0</span>
                            <span class="stat-label">Users Online</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" data-count="2847392">0</span>
                            <span class="stat-label">Gems Generated</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Generator Section -->
        <section class="generator-section">
            <div class="container">
                <div class="generator-card">
                    <div class="card-header">
                        <h2>Generate Free Gems</h2>
                        <p>Enter your username and select your platform to start generating gems</p>
                    </div>
                    
                    <div class="card-body">
                        <!-- Step 1: Username Input -->
                        <div class="step-container active" data-step="1">
                            <div class="step-header">
                                <div class="step-number">1</div>
                                <h3>Enter Your Username</h3>
                            </div>
                            <div class="input-group">
                                <span class="input-icon material-icons-outlined">person</span>
                                <input type="text" id="username" class="form-input" placeholder="Your Brawl Stars username" autocomplete="off" maxlength="20">
                                <div class="input-validation">
                                    <span class="validation-message"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Platform Selection -->
                        <div class="step-container" data-step="2">
                            <div class="step-header">
                                <div class="step-number">2</div>
                                <h3>Select Your Platform</h3>
                            </div>
                            <div class="platform-selection">
                                <div class="platform-option" data-platform="android">
                                    <div class="platform-icon">
                                        <span class="material-icons-outlined">android</span>
                                    </div>
                                    <span class="platform-name">Android</span>
                                </div>
                                <div class="platform-option" data-platform="ios">
                                    <div class="platform-icon">
                                        <span class="material-icons-outlined">phone_iphone</span>
                                    </div>
                                    <span class="platform-name">iOS</span>
                                </div>
                            </div>
                            <div class="platform-validation">
                                <span class="validation-message"></span>
                            </div>
                        </div>

                        <!-- Step 3: Gem Amount Selection -->
                        <div class="step-container" data-step="3">
                            <div class="step-header">
                                <div class="step-number">3</div>
                                <h3>Select Gem Amount</h3>
                            </div>
                            <div class="gem-selection">
                                <div class="gem-option" data-amount="1000">
                                    <div class="gem-icon">💎</div>
                                    <span class="gem-amount">1,000</span>
                                    <span class="gem-label">Gems</span>
                                </div>
                                <div class="gem-option" data-amount="5000">
                                    <div class="gem-icon">💎</div>
                                    <span class="gem-amount">5,000</span>
                                    <span class="gem-label">Gems</span>
                                </div>
                                <div class="gem-option" data-amount="10000">
                                    <div class="gem-icon">💎</div>
                                    <span class="gem-amount">10,000</span>
                                    <span class="gem-label">Gems</span>
                                </div>
                                <div class="gem-option" data-amount="25000">
                                    <div class="gem-icon">💎</div>
                                    <span class="gem-amount">25,000</span>
                                    <span class="gem-label">Gems</span>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button id="prev-btn" class="btn btn-secondary hidden">
                                <span class="material-icons-outlined">arrow_back</span>
                                Previous
                            </button>
                            <button id="next-btn" class="btn btn-primary">
                                Next
                                <span class="material-icons-outlined">arrow_forward</span>
                            </button>
                            <button id="generate-btn" class="btn btn-success hidden">
                                <span class="material-icons-outlined">auto_awesome</span>
                                Generate Gems
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Generation Process Modal -->
        <div id="generation-modal" class="modal hidden">
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Generating Your Gems</h3>
                </div>
                <div class="modal-body">
                    <div class="generation-progress">
                        <div class="progress-circle">
                            <svg class="progress-ring" width="120" height="120">
                                <circle class="progress-ring-circle" stroke="var(--primary-color)" stroke-width="4" fill="transparent" r="52" cx="60" cy="60"/>
                            </svg>
                            <div class="progress-percentage">0%</div>
                        </div>
                        <div class="generation-status">
                            <p class="status-text">Connecting to server...</p>
                            <div class="status-steps">
                                <div class="status-step active">Verifying username</div>
                                <div class="status-step">Connecting to game servers</div>
                                <div class="status-step">Generating gems</div>
                                <div class="status-step">Finalizing process</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success Modal -->
        <div id="success-modal" class="modal hidden">
            <div class="modal-backdrop"></div>
            <div class="modal-content success">
                <div class="modal-header">
                    <div class="success-icon">
                        <span class="material-icons-outlined">check_circle</span>
                    </div>
                    <h3>Gems Generated Successfully!</h3>
                </div>
                <div class="modal-body">
                    <div class="success-details">
                        <p>Your gems have been successfully generated and added to your account.</p>
                        <div class="generated-info">
                            <div class="info-item">
                                <span class="info-label">Username:</span>
                                <span class="info-value" id="final-username">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Platform:</span>
                                <span class="info-value" id="final-platform">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Gems Generated:</span>
                                <span class="info-value" id="final-gems">-</span>
                            </div>
                        </div>
                        <div class="success-actions">
                            <button id="restart-btn" class="btn btn-primary">
                                Generate More
                            </button>
                            <button id="close-btn" class="btn btn-secondary">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Background Elements -->
    <div class="background-elements">
        <div class="floating-gems">
            <div class="gem gem-1">💎</div>
            <div class="gem gem-2">💎</div>
            <div class="gem gem-3">💎</div>
            <div class="gem gem-4">💎</div>
            <div class="gem gem-5">💎</div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="src/js/config.js"></script>
    <script src="src/js/utils.js"></script>
    <script src="src/js/animations.js"></script>
    <script src="src/js/validation.js"></script>
    <script src="src/js/generator.js"></script>
    <script src="src/js/app.js"></script>
</body>
</html>
