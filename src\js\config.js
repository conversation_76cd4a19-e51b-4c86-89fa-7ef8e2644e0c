/**
 * Application Configuration
 * All configurable settings and constants
 */

const CONFIG = {
  // Application Info
  APP_NAME: 'Brawl Stars Gems Generator',
  VERSION: '3.0.0',
  
  // API Settings (if needed)
  API_BASE_URL: 'https://api.example.com',
  API_TIMEOUT: 10000,
  
  // Generation Settings
  GENERATION: {
    MIN_GEMS: 1000,
    MAX_GEMS: 25000,
    AVAILABLE_AMOUNTS: [1000, 5000, 10000, 25000],
    GENERATION_TIME: 15000, // 15 seconds
    PROGRESS_INTERVAL: 100, // Update progress every 100ms
  },
  
  // Validation Rules
  VALIDATION: {
    USERNAME: {
      MIN_LENGTH: 3,
      MAX_LENGTH: 20,
      PATTERN: /^[a-zA-Z0-9_-]+$/,
      FORBIDDEN_WORDS: ['admin', 'test', 'null', 'undefined']
    }
  },
  
  // Platform Configuration
  PLATFORMS: {
    ANDROID: {
      id: 'android',
      name: 'Android',
      icon: 'android'
    },
    IOS: {
      id: 'ios',
      name: 'iOS',
      icon: 'phone_iphone'
    }
  },
  
  // Animation Settings
  ANIMATIONS: {
    LOADING_DURATION: 3000,
    STEP_TRANSITION: 300,
    MODAL_TRANSITION: 300,
    PROGRESS_ANIMATION: 100,
    COUNTER_ANIMATION: 2000
  },
  
  // UI Settings
  UI: {
    LOADING_MESSAGES: [
      'Connecting to server...',
      'Verifying username...',
      'Connecting to game servers...',
      'Generating gems...',
      'Finalizing process...'
    ],
    SUCCESS_MESSAGES: [
      'Gems generated successfully!',
      'Your gems are ready!',
      'Generation completed!',
      'Enjoy your new gems!'
    ]
  },
  
  // Statistics (for display)
  STATS: {
    USERS_ONLINE: {
      min: 100000,
      max: 150000,
      updateInterval: 5000
    },
    GEMS_GENERATED: {
      min: 2000000,
      max: 3000000,
      updateInterval: 3000
    }
  },
  
  // Local Storage Keys
  STORAGE_KEYS: {
    USER_DATA: 'brawl_stars_user_data',
    SETTINGS: 'brawl_stars_settings',
    STATISTICS: 'brawl_stars_statistics'
  },
  
  // Error Messages
  ERRORS: {
    USERNAME_REQUIRED: 'Please enter your username',
    USERNAME_TOO_SHORT: 'Username must be at least 3 characters long',
    USERNAME_TOO_LONG: 'Username must be less than 20 characters long',
    USERNAME_INVALID: 'Username can only contain letters, numbers, underscores and hyphens',
    USERNAME_FORBIDDEN: 'This username is not allowed',
    PLATFORM_REQUIRED: 'Please select your platform',
    GEMS_REQUIRED: 'Please select gem amount',
    GENERATION_FAILED: 'Generation failed. Please try again.',
    NETWORK_ERROR: 'Network error. Please check your connection.',
    SERVER_ERROR: 'Server error. Please try again later.'
  },
  
  // Success Messages
  SUCCESS: {
    USERNAME_VALID: 'Username is valid',
    PLATFORM_SELECTED: 'Platform selected',
    GEMS_SELECTED: 'Gem amount selected',
    GENERATION_COMPLETE: 'Gems generated successfully!'
  },
  
  // Debug Settings
  DEBUG: {
    ENABLED: false, // Set to true for development
    LOG_LEVEL: 'info', // 'debug', 'info', 'warn', 'error'
    MOCK_GENERATION: false // Set to true to mock generation process
  }
};

// Freeze the configuration to prevent modifications
Object.freeze(CONFIG);

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CONFIG;
} else if (typeof window !== 'undefined') {
  window.CONFIG = CONFIG;
}
