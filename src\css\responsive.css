/**
 * Responsive Design
 * Mobile-first approach with breakpoints
 */

/* Mobile First - Base styles are for mobile */

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
  
  .generator-card {
    margin: var(--spacing-lg);
  }
  
  .platform-selection {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .gem-selection {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
  
  .header-content {
    flex-wrap: nowrap;
  }
  
  .header-stats {
    gap: var(--spacing-2xl);
  }
  
  .generator-section {
    padding: var(--spacing-3xl) 0;
  }
  
  .step-header {
    gap: var(--spacing-lg);
  }
  
  .step-number {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-xl);
  }
  
  .platform-selection {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
  }
  
  .gem-selection {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .action-buttons .btn {
    flex: none;
    min-width: 150px;
  }
  
  .modal-content {
    max-width: 600px;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
  
  .loading-logo .logo-img {
    width: 150px;
    height: 150px;
  }
  
  .loading-text h2 {
    font-size: var(--font-size-3xl);
  }
  
  .logo .logo-img {
    width: 70px;
    height: 70px;
  }
  
  .logo-text h1 {
    font-size: var(--font-size-2xl);
  }
  
  .generator-card {
    max-width: 700px;
  }
  
  .card-header h2 {
    font-size: var(--font-size-3xl);
  }
  
  .step-header h3 {
    font-size: var(--font-size-xl);
  }
  
  .platform-option {
    padding: var(--spacing-2xl);
  }
  
  .platform-icon {
    width: 80px;
    height: 80px;
    font-size: 32px;
  }
  
  .platform-name {
    font-size: var(--font-size-xl);
  }
  
  .gem-option {
    padding: var(--spacing-xl);
  }
  
  .gem-icon {
    font-size: 40px;
  }
  
  .gem-amount {
    font-size: var(--font-size-xl);
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
  
  .generator-section {
    padding: var(--spacing-3xl) 0;
  }
  
  .generator-card {
    max-width: 800px;
  }
  
  .header-stats {
    gap: var(--spacing-3xl);
  }
  
  .stat-number {
    font-size: var(--font-size-2xl);
  }
}

/* Extra extra large devices (1400px and up) */
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
}

/* Mobile Specific Styles */
@media (max-width: 575px) {
  .header {
    padding: var(--spacing-md) 0;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }
  
  .logo {
    justify-content: center;
  }
  
  .header-stats {
    gap: var(--spacing-lg);
  }
  
  .generator-section {
    padding: var(--spacing-xl) 0;
  }
  
  .generator-card {
    margin: var(--spacing-md);
  }
  
  .card-header {
    padding: var(--spacing-lg);
  }
  
  .card-header h2 {
    font-size: var(--font-size-xl);
  }
  
  .card-body {
    padding: var(--spacing-lg);
  }
  
  .step-container {
    margin-bottom: var(--spacing-lg);
  }
  
  .step-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }
  
  .platform-selection {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .platform-option {
    padding: var(--spacing-lg);
  }
  
  .platform-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .gem-selection {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
  
  .gem-option {
    padding: var(--spacing-md);
  }
  
  .gem-icon {
    font-size: 24px;
  }
  
  .gem-amount {
    font-size: var(--font-size-base);
  }
  
  .action-buttons {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .action-buttons .btn {
    max-width: none;
  }
  
  .modal-content {
    width: 95%;
    margin: var(--spacing-md);
  }
  
  .modal-header {
    padding: var(--spacing-lg);
  }
  
  .modal-body {
    padding: var(--spacing-lg);
  }
  
  .success-actions {
    flex-direction: column;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
  
  .loading-progress {
    width: 250px;
  }
  
  .status-steps {
    text-align: center;
  }
}

/* Landscape Mobile */
@media (max-width: 767px) and (orientation: landscape) {
  .generator-section {
    padding: var(--spacing-lg) 0;
  }
  
  .loading-screen {
    padding: var(--spacing-lg);
  }
  
  .loading-content {
    transform: scale(0.8);
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo-img,
  .loading-logo .logo-img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .floating-gems .gem {
    animation: none;
  }
  
  .animate-pulse,
  .animate-float,
  .animate-floatReverse {
    animation: none;
  }
  
  .spinner {
    animation-duration: 2s;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --white: #1a1a1a;
    --black: #ffffff;
    --gray-100: #2d2d2d;
    --gray-200: #3a3a3a;
    --gray-300: #4a4a4a;
    --gray-800: #e0e0e0;
    --gray-900: #f0f0f0;
  }
  
  .header {
    background: rgba(26, 26, 26, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .generator-card {
    background: rgba(26, 26, 26, 0.95);
  }
}

/* Print Styles */
@media print {
  .loading-screen,
  .background-elements,
  .floating-gems {
    display: none !important;
  }
  
  .generator-section {
    padding: 0;
  }
  
  .generator-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
