/**
 * Main Styles
 * Application-specific styles
 */

/* Body and App */
body {
  background: var(--bg-primary);
  min-height: 100vh;
  overflow-x: hidden;
}

.app {
  min-height: 100vh;
  position: relative;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  transition: all var(--transition-slow);
}

.loading-content {
  text-align: center;
  color: var(--white);
}

.loading-logo {
  margin-bottom: var(--spacing-xl);
}

.loading-logo .logo-img {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  border-radius: var(--border-radius-full);
  box-shadow: var(--shadow-xl);
}

.loading-spinner {
  margin: var(--spacing-xl) 0;
}

.loading-text h2 {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
}

.loading-text p {
  font-size: var(--font-size-lg);
  opacity: 0.8;
}

.loading-progress {
  margin-top: var(--spacing-xl);
  width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.progress-text {
  display: block;
  margin-top: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: var(--spacing-lg) 0;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo .logo-img {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

.logo-text h1 {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  margin: 0;
}

.logo-text span {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

.header-stats {
  display: flex;
  gap: var(--spacing-xl);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

/* Generator Section */
.generator-section {
  padding: var(--spacing-3xl) 0;
  min-height: calc(100vh - 120px);
  display: flex;
  align-items: center;
}

.generator-card {
  max-width: 600px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
}

/* Step Container */
.step-container {
  opacity: 0.3;
  transform: translateX(20px);
  transition: all var(--transition-normal);
  margin-bottom: var(--spacing-xl);
}

.step-container.active {
  opacity: 1;
  transform: translateX(0);
}

.step-container:last-child {
  margin-bottom: 0;
}

.step-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.step-number {
  width: 40px;
  height: 40px;
  background: var(--bg-primary);
  color: var(--white);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.step-header h3 {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

/* Platform Selection */
.platform-selection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.platform-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  background: var(--white);
}

.platform-option:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.platform-option.selected {
  border-color: var(--primary-color);
  background: rgba(74, 144, 226, 0.1);
}

.platform-icon {
  width: 60px;
  height: 60px;
  background: var(--bg-primary);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 24px;
}

.platform-name {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-lg);
  color: var(--gray-800);
}

/* Gem Selection */
.gem-selection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.gem-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  background: var(--white);
  text-align: center;
}

.gem-option:hover {
  border-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.gem-option.selected {
  border-color: var(--secondary-color);
  background: rgba(243, 156, 18, 0.1);
}

.gem-icon {
  font-size: 32px;
  margin-bottom: var(--spacing-sm);
}

.gem-amount {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  color: var(--gray-800);
}

.gem-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  justify-content: space-between;
  margin-top: var(--spacing-2xl);
}

.action-buttons .btn {
  flex: 1;
  max-width: 200px;
}

/* Generation Progress */
.generation-progress {
  text-align: center;
}

.progress-circle {
  margin-bottom: var(--spacing-xl);
}

.generation-status {
  margin-top: var(--spacing-xl);
}

.status-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--gray-800);
  margin-bottom: var(--spacing-lg);
}

.status-steps {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  text-align: left;
}

.status-step {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  background: var(--gray-100);
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal);
}

.status-step.active {
  background: rgba(74, 144, 226, 0.1);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.status-step.completed {
  background: rgba(39, 174, 96, 0.1);
  color: var(--success-color);
}

/* Success Modal */
.success-icon {
  width: 80px;
  height: 80px;
  background: var(--success-color);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  color: var(--white);
  font-size: 40px;
}

.success-details {
  text-align: center;
}

.generated-info {
  background: var(--gray-100);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--gray-200);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: var(--font-weight-medium);
  color: var(--gray-600);
}

.info-value {
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
}

.success-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

.success-actions .btn {
  flex: 1;
}

/* Background Elements */
.background-elements {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.floating-gems {
  position: absolute;
  width: 100%;
  height: 100%;
}

.gem {
  position: absolute;
  font-size: 24px;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.gem-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.gem-2 {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.gem-3 {
  top: 60%;
  left: 5%;
  animation-delay: 2s;
}

.gem-4 {
  bottom: 20%;
  right: 10%;
  animation-delay: 3s;
}

.gem-5 {
  bottom: 10%;
  left: 20%;
  animation-delay: 4s;
}
