/**
 * Animation Controller
 * Handles all animations and visual effects
 */

const AnimationController = {
  
  /**
   * Initialize animations
   */
  init() {
    this.setupLoadingAnimation();
    this.setupCounterAnimations();
    this.setupFloatingGems();
    Utils.Debug.log('info', 'Animation controller initialized');
  },
  
  /**
   * Setup loading screen animation
   */
  setupLoadingAnimation() {
    const loadingScreen = Utils.DOM.getElementById('loading-screen');
    const progressFill = Utils.DOM.querySelector('.progress-fill');
    const progressText = Utils.DOM.querySelector('.progress-text');
    
    if (!loadingScreen || !progressFill || !progressText) return;
    
    let progress = 0;
    const duration = CONFIG.ANIMATIONS.LOADING_DURATION;
    const interval = 50;
    const increment = (100 / duration) * interval;
    
    const progressTimer = setInterval(() => {
      progress += increment;
      
      if (progress >= 100) {
        progress = 100;
        clearInterval(progressTimer);
        
        // Hide loading screen after completion
        setTimeout(() => {
          this.hideLoadingScreen();
        }, 500);
      }
      
      progressFill.style.width = `${progress}%`;
      progressText.textContent = `${Math.floor(progress)}%`;
    }, interval);
  },
  
  /**
   * Hide loading screen
   */
  hideLoadingScreen() {
    const loadingScreen = Utils.DOM.getElementById('loading-screen');
    const app = Utils.DOM.getElementById('app');
    
    if (loadingScreen && app) {
      Utils.DOM.addClass(loadingScreen, 'animate-fadeOut');
      
      setTimeout(() => {
        Utils.DOM.addClass(loadingScreen, 'hidden');
        Utils.DOM.removeClass(app, 'hidden');
        Utils.DOM.addClass(app, 'animate-fadeIn');
        
        // Start header animations
        this.animateHeader();
      }, CONFIG.ANIMATIONS.MODAL_TRANSITION);
    }
  },
  
  /**
   * Animate header elements
   */
  animateHeader() {
    const logo = Utils.DOM.querySelector('.logo');
    const stats = Utils.DOM.querySelectorAll('.stat-item');
    
    if (logo) {
      Utils.Animation.animate(logo, 'animate-slideInLeft', 100);
    }
    
    stats.forEach((stat, index) => {
      Utils.Animation.animate(stat, 'animate-slideInRight', 200 + (index * 100));
    });
  },
  
  /**
   * Setup counter animations for statistics
   */
  setupCounterAnimations() {
    const counters = Utils.DOM.querySelectorAll('[data-count]');
    
    counters.forEach(counter => {
      const target = parseInt(counter.getAttribute('data-count'));
      if (target) {
        // Start counter animation after loading
        setTimeout(() => {
          Utils.Animation.animateCounter(counter, target, CONFIG.ANIMATIONS.COUNTER_ANIMATION);
        }, CONFIG.ANIMATIONS.LOADING_DURATION + 500);
      }
    });
  },
  
  /**
   * Setup floating gems animation
   */
  setupFloatingGems() {
    const gems = Utils.DOM.querySelectorAll('.floating-gems .gem');
    
    gems.forEach((gem, index) => {
      // Add random animation delay
      const delay = Utils.Numbers.random(0, 2000);
      gem.style.animationDelay = `${delay}ms`;
      
      // Add random size variation
      const scale = Utils.Numbers.random(80, 120) / 100;
      gem.style.transform = `scale(${scale})`;
      
      // Add random opacity variation
      const opacity = Utils.Numbers.random(30, 80) / 100;
      gem.style.opacity = opacity;
    });
  },
  
  /**
   * Animate step transition
   * @param {number} fromStep - Current step
   * @param {number} toStep - Target step
   */
  animateStepTransition(fromStep, toStep) {
    const currentStep = Utils.DOM.querySelector(`[data-step="${fromStep}"]`);
    const nextStep = Utils.DOM.querySelector(`[data-step="${toStep}"]`);
    
    if (currentStep) {
      Utils.DOM.removeClass(currentStep, 'active');
      Utils.Animation.animate(currentStep, 'animate-slideInLeft', 0);
    }
    
    if (nextStep) {
      setTimeout(() => {
        Utils.DOM.addClass(nextStep, 'active');
        Utils.Animation.animate(nextStep, 'animate-slideInRight', 0);
      }, CONFIG.ANIMATIONS.STEP_TRANSITION);
    }
  },
  
  /**
   * Show modal with animation
   * @param {string} modalId - Modal element ID
   */
  showModal(modalId) {
    const modal = Utils.DOM.getElementById(modalId);
    
    if (modal) {
      Utils.DOM.removeClass(modal, 'hidden');
      
      setTimeout(() => {
        Utils.DOM.addClass(modal, 'show');
      }, 10);
      
      Utils.Debug.log('info', `Modal shown: ${modalId}`);
    }
  },
  
  /**
   * Hide modal with animation
   * @param {string} modalId - Modal element ID
   */
  hideModal(modalId) {
    const modal = Utils.DOM.getElementById(modalId);
    
    if (modal) {
      Utils.DOM.removeClass(modal, 'show');
      
      setTimeout(() => {
        Utils.DOM.addClass(modal, 'hidden');
      }, CONFIG.ANIMATIONS.MODAL_TRANSITION);
      
      Utils.Debug.log('info', `Modal hidden: ${modalId}`);
    }
  },
  
  /**
   * Animate progress circle
   * @param {HTMLElement} circle - Progress circle element
   * @param {number} percentage - Progress percentage (0-100)
   */
  animateProgressCircle(circle, percentage) {
    if (!circle) return;
    
    const circumference = 2 * Math.PI * 52; // radius = 52
    const offset = circumference - (percentage / 100) * circumference;
    
    circle.style.strokeDashoffset = offset;
  },
  
  /**
   * Animate button click
   * @param {HTMLElement} button - Button element
   */
  animateButtonClick(button) {
    if (!button) return;
    
    Utils.Animation.animateOnce(button, 'animate-pulse', 300);
  },
  
  /**
   * Animate selection
   * @param {HTMLElement} element - Element to animate
   */
  animateSelection(element) {
    if (!element) return;
    
    Utils.Animation.animateOnce(element, 'animate-bounce', 600);
  },
  
  /**
   * Animate error
   * @param {HTMLElement} element - Element to animate
   */
  animateError(element) {
    if (!element) return;
    
    Utils.Animation.animateOnce(element, 'animate-shake', 600);
  },
  
  /**
   * Animate success
   * @param {HTMLElement} element - Element to animate
   */
  animateSuccess(element) {
    if (!element) return;
    
    Utils.Animation.animateOnce(element, 'animate-pulse', 600);
    Utils.DOM.addClass(element, 'animate-glow');
    
    setTimeout(() => {
      Utils.DOM.removeClass(element, 'animate-glow');
    }, 2000);
  },
  
  /**
   * Create sparkle effect
   * @param {HTMLElement} element - Target element
   */
  createSparkleEffect(element) {
    if (!element) return;
    
    for (let i = 0; i < 5; i++) {
      const sparkle = document.createElement('div');
      sparkle.className = 'sparkle';
      sparkle.style.position = 'absolute';
      sparkle.style.width = '4px';
      sparkle.style.height = '4px';
      sparkle.style.background = '#ffd700';
      sparkle.style.borderRadius = '50%';
      sparkle.style.pointerEvents = 'none';
      
      const rect = element.getBoundingClientRect();
      sparkle.style.left = `${rect.left + Utils.Numbers.random(0, rect.width)}px`;
      sparkle.style.top = `${rect.top + Utils.Numbers.random(0, rect.height)}px`;
      
      document.body.appendChild(sparkle);
      
      Utils.Animation.animate(sparkle, 'animate-sparkle', i * 100);
      
      setTimeout(() => {
        if (sparkle.parentNode) {
          sparkle.parentNode.removeChild(sparkle);
        }
      }, 1500);
    }
  }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AnimationController;
} else if (typeof window !== 'undefined') {
  window.AnimationController = AnimationController;
}
